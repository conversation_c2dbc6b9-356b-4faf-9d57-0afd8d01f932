import { createApp } from 'vue';
import App from './App.vue';
import { createPinia } from 'pinia';
import i18n, { loadLocaleMessages } from '@/lib/i18n'
import router from './router';
import liff from "@line/liff";
import { useGameplayStateManager } from '@/features/gameplay/stores/gameplayStateManager';
import { useThemeAssets } from '@/themes';
import { useTheme } from '@/themes/useTheme';
import { applyTheme } from '@/themes/applyTheme';
import { createAppKit } from '@reown/appkit/vue'
// import { mainnet } from '@reown/appkit/networks'
import { defaultNetwork } from './config/networks'
import { Ethers5Adapter } from "@reown/appkit-adapter-ethers5";
import { useNetworkSwitchHandler } from './features/auth/composables/useNetworkSwitchHandler';

const themeAssets = useThemeAssets();
applyTheme(themeAssets.value);

// Safari兼容性：使用DOMContentLoaded替代load事件，并添加更好的错误处理
function initializeApp() {
    return new Promise(async (resolve, reject) => {
        try {
          

            // Resolve current theme and load corresponding locale messages first
            const { themeName } = useTheme();

            // 添加超时处理，防止无限等待
            const loadLocalePromise = loadLocaleMessages(i18n.global.locale.value, themeName.value);
            const timeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Locale loading timeout')), 10000)
            );

            await Promise.race([loadLocalePromise, timeoutPromise]);

            // AppKit初始化（仅在gb主题时）
            if(themeName.value === 'gb') {
                try {
                    await createAppKit({
                        adapters: [new Ethers5Adapter()],
                        networks: [defaultNetwork],
                        projectId: import.meta.env.VITE_APP_KIT_PROJECT_ID,
                        metadata: {
                            name: 'GooseBox',
                            description: 'GooseBox Game',
                            url: window.location.origin,
                            icons: ['https://avatars.githubusercontent.com/u/37784886'],
                        },
                    });
                } catch (appKitError) {
                    console.warn("AppKit initialization failed, continuing without it:", appKitError);
                }
            }

            // 创建并挂载Vue应用
            const app = createApp(App);
            app.use(i18n).use(createPinia()).use(router);

            // Safari兼容性：确保DOM元素存在
            const appElement = document.getElementById('app');
            if (!appElement) {
                throw new Error('App element not found');
            }

            app.mount('#app');

        
            // 初始化游戏状态管理器
            try {
                const gameplayStateManager = useGameplayStateManager();
                gameplayStateManager.enterGameplay();
            } catch (gameplayError) {
                console.warn("Gameplay state manager initialization failed:", gameplayError);
            }

            // 初始化网络切换处理器
            try {
                const networkSwitchHandler = useNetworkSwitchHandler();
                networkSwitchHandler.handleNetworkEvents();
            } catch (networkError) {
                console.warn("Network switch handler initialization failed:", networkError);
            }

            // 应用退出时的清理
            window.addEventListener('beforeunload', async (event) => {
                try {
                    const gameplayStateManager = useGameplayStateManager();
                    await gameplayStateManager.exitGameplay();
                } catch (error) {
                    console.warn("Error during app cleanup:", error);
                }
            });

            // LIFF初始化
            try {
                liff.init({
                    liffId: import.meta.env.VITE_LINE_LIFF_ID,
                });
            } catch (liffError) {
                console.warn("LIFF initialization failed:", liffError);
            }

            resolve();
        } catch (error) {
            console.error("Error initializing application:", error);
            reject(error);
        }
    });
}


// Safari兼容性：使用多种事件监听器确保应用能够正确初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    // DOM已经加载完成
    initializeApp();
}

// 备用方案：如果DOMContentLoaded没有触发，使用load事件
window.addEventListener('load', () => {
    // 检查应用是否已经初始化
    const appElement = document.getElementById('app');
    if (appElement && appElement.innerHTML.trim() === '') {
        console.warn('App not initialized, trying backup initialization');
        initializeApp();
    }
});
